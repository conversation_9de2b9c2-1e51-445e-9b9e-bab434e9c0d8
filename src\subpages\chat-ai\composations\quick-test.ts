/**
 * 快速测试解析器
 */

import { parseStreamingData, resetStreamingParser } from "./useParseStreaming";

// 测试最基本的情况
console.log("=== 快速测试解析器 ===");

resetStreamingParser();

// 创建一个符合ChunkData接口的测试数据
const testMessage = {
  session_id: "test-session",
  message_id: "msg1",
  intention_id: 1,
  event_time: Date.now(),
  content: "Hello World",
  think: "",
  description: "Test message",
  id: "msg1",
  duration: 0,
  is_card: false,
  event_type: "message"
};

const testChunkData = {
  message: testMessage,
  event_type: "message"
};

const testData = `data: ${JSON.stringify(testChunkData)}`;

console.log("测试数据:");
console.log(testData);

console.log("\n开始解析...");
const results = parseStreamingData(testData);

console.log("\n最终结果:");
console.log("结果数量:", results.length);
console.log("结果内容:", results);

if (results.length > 0) {
  console.log("✅ 解析成功！");
  console.log("消息内容:", results[0].message?.content);
} else {
  console.log("❌ 解析失败，没有得到任何结果");
}
