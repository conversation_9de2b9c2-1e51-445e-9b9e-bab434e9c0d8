/**
 * 最终测试 - 验证优化后的解析器
 */

import { parseStreamingData, resetStreamingParser, hasUnprocessedData } from "./useParseStreaming";

console.log("=== 最终解析器测试 ===");

// 测试1: 基本功能
function testBasic() {
  console.log("\n1. 基本功能测试:");
  resetStreamingParser();
  
  const data = 'data: {"message": {"content": "Hello"}, "event_type": "message"}';
  const results = parseStreamingData(data);
  
  console.log("输入:", data);
  console.log("结果数量:", results.length);
  console.log("结果:", results);
  
  if (results.length === 1) {
    console.log("✅ 基本功能正常");
  } else {
    console.log("❌ 基本功能异常");
  }
}

// 测试2: 截断拼接
function testTruncation() {
  console.log("\n2. 截断拼接测试:");
  resetStreamingParser();
  
  const part1 = 'data: {"message": {"content": "被截断的';
  const part2 = '消息"}, "event_type": "message"}';
  
  console.log("Part1:", part1);
  console.log("Part2:", part2);
  
  const result1 = parseStreamingData(part1);
  console.log("处理part1: 结果数量=", result1.length, ", 有未处理数据=", hasUnprocessedData());
  
  const result2 = parseStreamingData(part2);
  console.log("处理part2: 结果数量=", result2.length, ", 有未处理数据=", hasUnprocessedData());
  
  const total = result1.length + result2.length;
  if (total === 1) {
    console.log("✅ 截断拼接正常");
  } else {
    console.log("❌ 截断拼接异常");
  }
}

// 测试3: 混合数据
function testMixed() {
  console.log("\n3. 混合数据测试:");
  resetStreamingParser();
  
  const chunk1 = `data: {"message": {"content": "完整1"}, "event_type": "message"}
data: {"message": {"content": "不完整`;
  
  const chunk2 = `2"}, "event_type": "message"}
data: {"message": {"content": "完整3"}, "event_type": "message"}`;
  
  console.log("Chunk1: 1完整 + 1不完整开始");
  console.log("Chunk2: 1不完整结束 + 1完整");
  
  const result1 = parseStreamingData(chunk1);
  console.log("处理chunk1: 结果数量=", result1.length);
  
  const result2 = parseStreamingData(chunk2);
  console.log("处理chunk2: 结果数量=", result2.length);
  
  const total = result1.length + result2.length;
  console.log("总结果数量:", total);
  
  if (total === 3) {
    console.log("✅ 混合数据正常");
  } else {
    console.log("❌ 混合数据异常");
  }
}

// 测试4: data:前缀截断
function testDataPrefix() {
  console.log("\n4. data:前缀截断测试:");
  resetStreamingParser();
  
  const chunks = ["da", "ta: ", '{"test": "value"}'];
  
  let totalResults = 0;
  chunks.forEach((chunk, index) => {
    const results = parseStreamingData(chunk);
    totalResults += results.length;
    console.log(`Chunk${index + 1} "${chunk}": 结果数量=${results.length}`);
  });
  
  if (totalResults === 1) {
    console.log("✅ data:前缀截断正常");
  } else {
    console.log("❌ data:前缀截断异常");
  }
}

// 运行所有测试
function runAllTests() {
  testBasic();
  testTruncation();
  testMixed();
  testDataPrefix();
  
  console.log("\n=== 测试完成 ===");
}

// 执行测试
runAllTests();
