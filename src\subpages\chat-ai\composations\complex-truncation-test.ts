/**
 * 复杂截断情况测试
 * 测试各种复杂的数据截断场景
 */

import { parseStreamingData, resetStreamingParser, hasUnprocessedData } from "./useParseStreaming";

/**
 * 测试情况1: JSON对象被分割成多个chunk
 */
function testJsonSplitAcrossChunks() {
  console.log("=== 测试情况1: JSON对象被分割成多个chunk ===");
  resetStreamingParser();

  // 创建一个完整的JSON，然后分割成多个部分
  const completeJson = {
    message: {
      session_id: "test-session",
      message_id: "msg1",
      content: "这是一个被分割的消息",
      event_type: "message"
    },
    event_type: "message"
  };

  const completeData = `data: ${JSON.stringify(completeJson)}`;
  console.log("完整数据:", completeData);

  // 分割成3个chunk
  const chunk1 = completeData.substring(0, 30);  // "data: {\"message\": {\"session_"
  const chunk2 = completeData.substring(30, 80); // "id\": \"test-session\", \"message_id\": \"msg1\", \"content\": \"这是一个被分割的消息\", \"event_type\": \"message\"}, \"event_type\": \"message\"}"
  const chunk3 = completeData.substring(80);     // 剩余部分

  console.log("Chunk1:", chunk1);
  console.log("Chunk2:", chunk2);
  console.log("Chunk3:", chunk3);

  // 逐个处理chunk
  const result1 = parseStreamingData(chunk1);
  console.log("处理chunk1后:", result1.length, "条消息, 有未处理数据:", hasUnprocessedData());

  const result2 = parseStreamingData(chunk2);
  console.log("处理chunk2后:", result2.length, "条消息, 有未处理数据:", hasUnprocessedData());

  const result3 = parseStreamingData(chunk3);
  console.log("处理chunk3后:", result3.length, "条消息, 有未处理数据:", hasUnprocessedData());

  const totalResults = result1.length + result2.length + result3.length;
  console.log("总共解析到:", totalResults, "条消息");

  if (totalResults === 1) {
    console.log("✅ 测试通过：成功处理分割的JSON");
  } else {
    console.log("❌ 测试失败：未能正确处理分割的JSON");
  }
}

/**
 * 测试情况2: data:前缀被截断
 */
function testDataPrefixTruncation() {
  console.log("\n=== 测试情况2: data:前缀被截断 ===");
  resetStreamingParser();

  // data:前缀被截断的情况
  const chunk1 = "da";
  const chunk2 = "ta: ";
  const chunk3 = '{"message": {"content": "测试消息"}, "event_type": "message"}';

  console.log("Chunk1:", chunk1);
  console.log("Chunk2:", chunk2);
  console.log("Chunk3:", chunk3);

  const result1 = parseStreamingData(chunk1);
  console.log("处理chunk1后:", result1.length, "条消息");

  const result2 = parseStreamingData(chunk2);
  console.log("处理chunk2后:", result2.length, "条消息");

  const result3 = parseStreamingData(chunk3);
  console.log("处理chunk3后:", result3.length, "条消息");

  const totalResults = result1.length + result2.length + result3.length;
  console.log("总共解析到:", totalResults, "条消息");

  if (totalResults === 1) {
    console.log("✅ 测试通过：成功处理data:前缀截断");
  } else {
    console.log("❌ 测试失败：未能正确处理data:前缀截断");
  }
}

/**
 * 测试情况3: 混合完整和不完整数据块
 */
function testMixedCompleteIncomplete() {
  console.log("\n=== 测试情况3: 混合完整和不完整数据块 ===");
  resetStreamingParser();

  // 第一个chunk包含一个完整的data块和一个不完整的data块
  const chunk1 = `data: {"message": {"content": "完整消息1"}, "event_type": "message"}
data: {"message": {"content": "不完整`;

  // 第二个chunk包含不完整数据的续传和一个新的完整数据块
  const chunk2 = `消息2"}, "event_type": "message"}
data: {"message": {"content": "完整消息3"}, "event_type": "message"}`;

  console.log("Chunk1包含: 1个完整 + 1个不完整开始");
  console.log("Chunk2包含: 1个不完整结束 + 1个完整");

  const result1 = parseStreamingData(chunk1);
  console.log("处理chunk1后:", result1.length, "条消息, 有未处理数据:", hasUnprocessedData());

  const result2 = parseStreamingData(chunk2);
  console.log("处理chunk2后:", result2.length, "条消息, 有未处理数据:", hasUnprocessedData());

  const totalResults = result1.length + result2.length;
  console.log("总共解析到:", totalResults, "条消息");

  if (totalResults === 3) {
    console.log("✅ 测试通过：成功处理混合数据块");
  } else {
    console.log("❌ 测试失败：未能正确处理混合数据块");
  }
}

/**
 * 测试情况4: 在JSON字符串内部截断
 */
function testTruncationInJsonString() {
  console.log("\n=== 测试情况4: 在JSON字符串内部截断 ===");
  resetStreamingParser();

  // 在JSON字符串内部截断
  const chunk1 = 'data: {"message": {"content": "这个字符串在中间被截';
  const chunk2 = '断了，但是应该能正确处理"}, "event_type": "message"}';

  console.log("Chunk1:", chunk1);
  console.log("Chunk2:", chunk2);

  const result1 = parseStreamingData(chunk1);
  console.log("处理chunk1后:", result1.length, "条消息");

  const result2 = parseStreamingData(chunk2);
  console.log("处理chunk2后:", result2.length, "条消息");

  const totalResults = result1.length + result2.length;
  console.log("总共解析到:", totalResults, "条消息");

  if (totalResults === 1) {
    console.log("✅ 测试通过：成功处理字符串内部截断");
  } else {
    console.log("❌ 测试失败：未能正确处理字符串内部截断");
  }
}

/**
 * 测试情况5: 极端小chunk
 */
function testVerySmallChunks() {
  console.log("\n=== 测试情况5: 极端小chunk ===");
  resetStreamingParser();

  const completeData = 'data: {"test": "value"}';
  
  // 每次只发送一个字符
  let totalResults = 0;
  for (let i = 0; i < completeData.length; i++) {
    const chunk = completeData[i];
    const results = parseStreamingData(chunk);
    totalResults += results.length;
  }

  console.log("逐字符发送，总共解析到:", totalResults, "条消息");

  if (totalResults === 1) {
    console.log("✅ 测试通过：成功处理极端小chunk");
  } else {
    console.log("❌ 测试失败：未能正确处理极端小chunk");
  }
}

/**
 * 运行所有复杂截断测试
 */
export function runComplexTruncationTests() {
  console.log("开始复杂截断情况测试...\n");

  testJsonSplitAcrossChunks();
  testDataPrefixTruncation();
  testMixedCompleteIncomplete();
  testTruncationInJsonString();
  testVerySmallChunks();

  console.log("\n=== 复杂截断测试完成 ===");
}

// 如果直接运行此文件，执行测试
if (typeof window === "undefined") {
  runComplexTruncationTests();
}
