import type { ChunkData } from "@/api/servers/chat/type";

/**
 * 流式解析器状态管理
 */
interface StreamParserState {
  buffer: string;
  lastProcessedIndex: number;
  isStreamEnded: boolean;
}

// 全局状态，用于存储解析状态
let parserState: StreamParserState = {
  buffer: "",
  lastProcessedIndex: 0,
  isStreamEnded: false,
};

/**
 * 优化的流式数据解析器
 * 支持复杂的截断情况：
 * 1. 单个JSON对象被分割成多个chunk
 * 2. 一个chunk包含多个完整和不完整的data块
 * 3. data:前缀本身被截断
 * 4. JSON对象在任意位置被截断
 *
 * @param data - 新接收到的数据块
 * @returns 解析后的JSON对象数组
 */
export function parseStreamingData(data: string): ChunkData[] {
  // 将新数据追加到缓存中
  parserState.buffer += data;

  const parsedData: ChunkData[] = [];

  // 持续处理缓冲区直到没有更多完整的数据块
  while (true) {
    const parseResult = tryParseNextDataBlock();

    if (parseResult.success && parseResult.data) {
      // 成功解析到完整数据，添加到结果中
      parsedData.push(parseResult.data);

      // 立即清空已处理的部分
      parserState.buffer = parserState.buffer.substring(parseResult.endIndex);

      // 继续处理剩余数据
      continue;
    } else if (parseResult.shouldWait) {
      // 数据不完整，需要等待更多数据
      break;
    } else if (parseResult.shouldSkip) {
      // 跳过无效数据
      parserState.buffer = parserState.buffer.substring(
        parseResult.skipLength || 1,
      );
      continue;
    } else {
      // 没有找到更多数据块
      break;
    }
  }

  return parsedData;
}

/**
 * 解析结果接口
 */
interface ParseResult {
  success: boolean;
  data?: ChunkData;
  endIndex: number;
  shouldWait: boolean; // 是否应该等待更多数据
  shouldSkip: boolean; // 是否应该跳过当前位置
  skipLength?: number; // 跳过的长度
}

/**
 * 尝试解析下一个data块
 */
function tryParseNextDataBlock(): ParseResult {
  const buffer = parserState.buffer;

  // 如果缓冲区为空，没有数据可处理
  if (buffer.length === 0) {
    return {
      success: false,
      endIndex: 0,
      shouldWait: false,
      shouldSkip: false,
    };
  }

  // 情况1: 缓冲区开头可能是被截断的JSON（没有data:前缀）
  if (!buffer.trim().startsWith("data:")) {
    return tryParseIncompleteJson(0);
  }

  // 情况2: 查找完整的"data:"前缀
  const dataIndex = findCompleteDataPrefix(buffer, 0);

  if (dataIndex === -1) {
    // 没有找到完整的data:前缀
    if (buffer.length < 5) {
      // 缓冲区太短，可能data:前缀被截断，等待更多数据
      return {
        success: false,
        endIndex: 0,
        shouldWait: true,
        shouldSkip: false,
      };
    } else {
      // 缓冲区足够长但没有data:，跳过第一个字符
      return {
        success: false,
        endIndex: 0,
        shouldWait: false,
        shouldSkip: true,
        skipLength: 1,
      };
    }
  }

  // 情况3: 找到data:前缀，尝试解析JSON
  const jsonStart = findJsonStart(buffer, dataIndex + 5);

  if (jsonStart === -1) {
    // data:后面没有找到JSON开始标记
    if (buffer.length - dataIndex < 10) {
      // 可能JSON开始部分被截断，等待更多数据
      return {
        success: false,
        endIndex: 0,
        shouldWait: true,
        shouldSkip: false,
      };
    } else {
      // 跳过这个无效的data:
      return {
        success: false,
        endIndex: 0,
        shouldWait: false,
        shouldSkip: true,
        skipLength: dataIndex + 5,
      };
    }
  }

  // 情况4: 尝试解析JSON对象
  return tryParseIncompleteJson(jsonStart);
}

/**
 * 查找完整的data:前缀
 */
function findCompleteDataPrefix(buffer: string, startIndex: number): number {
  const dataPrefix = "data:";
  const index = buffer.indexOf(dataPrefix, startIndex);

  if (index === -1) {
    return -1;
  }

  // 检查是否有足够的字符构成完整的data:前缀
  if (index + dataPrefix.length <= buffer.length) {
    return index;
  }

  // 如果到了缓冲区末尾但前缀不完整，返回-1
  return -1;
}

/**
 * 尝试解析可能不完整的JSON
 */
function tryParseIncompleteJson(startIndex: number): ParseResult {
  const buffer = parserState.buffer;

  if (startIndex >= buffer.length) {
    return {
      success: false,
      endIndex: startIndex,
      shouldWait: true,
      shouldSkip: false,
    };
  }

  // 如果不是以{开始，可能不是JSON
  if (buffer[startIndex] !== "{") {
    // 查找下一个{
    const nextBrace = buffer.indexOf("{", startIndex);
    if (nextBrace === -1) {
      // 没有找到{，可能需要等待更多数据
      if (buffer.length - startIndex < 50) {
        return {
          success: false,
          endIndex: startIndex,
          shouldWait: true,
          shouldSkip: false,
        };
      } else {
        // 跳过当前位置
        return {
          success: false,
          endIndex: startIndex,
          shouldWait: false,
          shouldSkip: true,
          skipLength: 1,
        };
      }
    } else {
      // 跳到{的位置
      return tryParseIncompleteJson(nextBrace);
    }
  }

  // 尝试提取完整的JSON
  const jsonResult = extractCompleteJsonFromPosition(buffer, startIndex);

  if (jsonResult.jsonStr) {
    // 找到完整的JSON，尝试解析
    try {
      const parsed = JSON.parse(jsonResult.jsonStr);
      if (typeof parsed === "object" && parsed !== null) {
        return {
          success: true,
          data: parsed as ChunkData,
          endIndex: startIndex + jsonResult.jsonStr.length,
          shouldWait: false,
          shouldSkip: false,
        };
      }
    } catch {
      // JSON解析失败，跳过这个数据块
      return {
        success: false,
        endIndex: startIndex,
        shouldWait: false,
        shouldSkip: true,
        skipLength: startIndex + jsonResult.jsonStr.length,
      };
    }
  }

  // JSON不完整，需要等待更多数据
  return {
    success: false,
    endIndex: startIndex,
    shouldWait: true,
    shouldSkip: false,
  };
}

/**
 * 查找JSON开始位置
 */
function findJsonStart(buffer: string, startIndex: number): number {
  let index = startIndex;

  // 跳过空白字符
  while (index < buffer.length && /\s/.test(buffer[index])) {
    index++;
  }

  // 检查是否找到JSON开始标记
  if (index < buffer.length && buffer[index] === "{") {
    return index;
  }

  return -1;
}

/**
 * 从指定位置开始提取完整的JSON对象字符串
 * 返回解析结果，包含JSON字符串和是否完整的标识
 */
function extractCompleteJsonFromPosition(
  data: string,
  startIndex: number,
): {
  jsonStr: string | null;
  isComplete: boolean;
} {
  const len = data.length;

  if (startIndex >= len || data[startIndex] !== "{") {
    return { jsonStr: null, isComplete: false };
  }

  let braceCount = 0;
  let inString = false;
  let escapeNext = false;

  for (let i = startIndex; i < len; i++) {
    const char = data[i];

    if (escapeNext) {
      escapeNext = false;
      continue;
    }

    if (char === "\\") {
      escapeNext = true;
      continue;
    }

    if (char === '"') {
      inString = !inString;
      continue;
    }

    if (!inString) {
      if (char === "{") {
        braceCount++;
      } else if (char === "}") {
        braceCount--;

        // 找到完整的JSON对象
        if (braceCount === 0) {
          const jsonStr = data.substring(startIndex, i + 1);
          return {
            jsonStr,
            isComplete: true,
          };
        }

        // 括号不匹配
        if (braceCount < 0) {
          return { jsonStr: null, isComplete: false };
        }
      }
    }
  }

  // 到达字符串末尾但JSON不完整
  return { jsonStr: null, isComplete: false };
}

/**
 * 批量处理多个数据块
 * 适用于一次性接收到多个chunk的情况
 *
 * @param chunks - 数据块数组
 * @returns 解析后的JSON对象数组
 */
export function parseStreamingDataBatch(chunks: string[]): ChunkData[] {
  const allParsedData: ChunkData[] = [];

  for (let i = 0; i < chunks.length; i++) {
    const parsedData = parseStreamingData(chunks[i]);
    allParsedData.push(...parsedData);
  }

  return allParsedData;
}

/**
 * 获取解析器当前状态信息
 * 用于调试和监控
 */
export function getParserState(): Readonly<StreamParserState> {
  return { ...parserState };
}

/**
 * 检查缓冲区是否有未处理的数据
 */
export function hasUnprocessedData(): boolean {
  return parserState.buffer.length > 0;
}

/**
 * 强制处理缓冲区中的剩余数据
 * 在流意外中断时使用
 */
export function flushRemainingData(): ChunkData[] {
  if (parserState.buffer.length === 0) {
    return [];
  }

  // 尝试解析缓冲区中的剩余数据
  const remainingData = parserState.buffer;
  resetStreamingParser(); // 清空缓冲区

  // 如果剩余数据看起来像是不完整的JSON，尝试解析
  const results: ChunkData[] = [];
  if (remainingData.trim()) {
    console.warn("强制处理剩余数据:", `${remainingData.substring(0, 100)}...`);
    // 这里可以尝试一些恢复策略，但通常不完整的数据无法解析
  }

  return results;
}

/**
 * 重置解析器的内部状态
 */
export function resetStreamingParser(): void {
  parserState = {
    buffer: "",
    lastProcessedIndex: 0,
    isStreamEnded: false,
  };
}
