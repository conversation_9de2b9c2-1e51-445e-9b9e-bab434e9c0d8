import type { ChunkData } from "@/api/servers/chat/type";

// 全局缓存，用于存储未完成的数据
let incompleteBuffer = "";

/**
 * 自动检测数据块是否完整
 */
function isDataComplete(buffer: string): boolean {
  if (!buffer.trim()) return false;

  // 检查是否有未完成的data:块
  let isComplete = true;
  const dataBlocks = buffer.split("data:");
  for (let i = 0; i < dataBlocks.length; i++) {
    const item = dataBlocks[i].trim();
    const result = extractCompleteJsonFromPosition(item, 0);
    if (!result.isComplete) {
      isComplete = result.isComplete;
      break;
    }
  }
  return isComplete;
}

/**
 * 去除流式数据中的"data:"前缀，并将剩余部分解析为JSON对象数组。
 * 支持包含Markdown文本（可能包含换行符）的JSON对象。
 * 支持增量式数据接收，处理被切断的JSON对象。
 * 支持处理跨chunk的JSON对象（即使没有data:前缀的续传数据）。
 *
 * @param data - 包含一个或多个JSON对象的输入字符串，每个JSON对象以"data:"开头
 * @returns 解析后的JSON对象数组
 * @throws SyntaxError 如果输入字符串包含无效的JSON
 */
export function parseStreamingData(data: string): ChunkData[] {
  const parsedData: ChunkData[] = [];

  // 将新数据追加到缓存中，逐步构建完整的数据流
  incompleteBuffer += data;
  const isComplete = isDataComplete(incompleteBuffer);

  // 处理缓存中的数据，提取所有完整的 data: 块
  let processedLength = 0;

  while (processedLength < incompleteBuffer.length) {
    // 首先检查是否有未完成的JSON对象（没有data:前缀的情况）
    if (processedLength === 0 && !incompleteBuffer.trim().startsWith("data:")) {
      // 尝试从缓存开始位置解析JSON（可能是跨chunk的续传数据）
      const result = extractCompleteJsonFromPosition(incompleteBuffer, 0);

      if (result.jsonStr) {
        try {
          const parsed = JSON.parse(result.jsonStr);
          if (typeof parsed === "object" && parsed !== null) {
            parsedData.push(parsed);
          }
        } catch (error) {
          console.warn(
            `无法解析续传JSON字符串: ${result.jsonStr.substring(
              0,
              100,
            )}...。错误: ${(error as Error).message}`,
          );
        }
        processedLength = result.jsonStr.length;
        continue;
      } else if (!result.isComplete && !isComplete) {
        // 数据不完整且流未结束，等待更多数据
        break;
      }
    }

    // 查找下一个 "data:" 标记
    const dataIndex = incompleteBuffer.indexOf("data:", processedLength);
    if (dataIndex === -1) {
      break;
    }

    // 跳过 "data:" 和空白字符，找到JSON开始位置
    let jsonStart = dataIndex + 5;
    while (
      jsonStart < incompleteBuffer.length &&
      /\s/.test(incompleteBuffer[jsonStart])
    ) {
      jsonStart++;
    }

    if (
      jsonStart >= incompleteBuffer.length ||
      incompleteBuffer[jsonStart] !== "{"
    ) {
      // 没有找到JSON开始标记，或者数据不够，跳过这个位置
      processedLength = dataIndex + 5;
      continue;
    }

    // 尝试提取完整的JSON对象
    const result = extractCompleteJsonFromPosition(incompleteBuffer, jsonStart);

    if (result.jsonStr) {
      // 找到完整的JSON对象，解析它
      try {
        const parsed = JSON.parse(result.jsonStr);
        if (typeof parsed === "object" && parsed !== null) {
          parsedData.push(parsed);
        }
      } catch (error) {
        console.warn(
          `无法解析JSON字符串: ${result.jsonStr.substring(0, 100)}...。错误: ${
            (error as Error).message
          }`,
        );
      }

      // 更新已处理的长度
      processedLength = jsonStart + result.jsonStr.length;
    } else {
      processedLength = dataIndex + 5;
    }
  }

  // 移除已处理的数据，保留未处理的部分
  if (processedLength > 0) {
    incompleteBuffer = incompleteBuffer.substring(processedLength);
  }

  // 如果数据流已完成，清空缓存
  if (isComplete) {
    resetStreamingParser();
  }

  console.log("解析到的数据条数:", parsedData);
  console.log("缓存的数据长度:", incompleteBuffer.length);

  return parsedData;
}

/**
 * 从指定位置开始提取完整的JSON对象字符串
 * 返回解析结果，包含JSON字符串和是否完整的标识
 */
function extractCompleteJsonFromPosition(
  data: string,
  startIndex: number,
): {
  jsonStr: string | null;
  isComplete: boolean;
} {
  const len = data.length;
  if (startIndex >= len || data[startIndex] !== "{") {
    return { jsonStr: null, isComplete: false };
  }

  // 动态规划状态定义
  const dp: Array<{
    inString: boolean;
    escapeNext: boolean;
    braceCount: number;
    isValid: boolean;
  }> = Array.from({ length: len - startIndex });

  // 初始状态
  dp[0] = {
    inString: false,
    escapeNext: false,
    braceCount: 1, // 第一个字符是 '{'
    isValid: true,
  };

  // 动态规划转移
  for (let i = 1; i < len - startIndex; i++) {
    const char = data[startIndex + i];
    const prev = dp[i - 1];

    // 如果前一个状态无效，当前状态也无效
    if (!prev.isValid) {
      dp[i] = { ...prev, isValid: false };
      continue;
    }

    const currentState = {
      inString: prev.inString,
      escapeNext: false,
      braceCount: prev.braceCount,
      isValid: true,
    };

    // 处理转义字符
    if (prev.escapeNext) {
      // 前一个字符是转义字符，当前字符被转义
      dp[i] = currentState;
      continue;
    }

    if (char === "\\") {
      currentState.escapeNext = true;
    } else if (char === '"') {
      // 引号切换字符串状态
      currentState.inString = !prev.inString;
    } else if (!prev.inString) {
      // 不在字符串内部，处理括号
      if (char === "{") {
        currentState.braceCount++;
      } else if (char === "}") {
        currentState.braceCount--;

        // 找到完整的JSON对象
        if (currentState.braceCount === 0) {
          return {
            jsonStr: data.substring(startIndex, startIndex + i + 1),
            isComplete: true,
          };
        }

        // 括号不匹配，状态无效
        if (currentState.braceCount < 0) {
          currentState.isValid = false;
        }
      }
    }

    dp[i] = currentState;
  }

  return { jsonStr: null, isComplete: false };
}

/**
 * 重置解析器的内部缓存
 */
export function resetStreamingParser(): void {
  incompleteBuffer = "";
}
