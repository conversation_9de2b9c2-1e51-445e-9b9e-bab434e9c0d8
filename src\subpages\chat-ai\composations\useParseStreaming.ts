import type { ChunkData } from "@/api/servers/chat/type";

/**
 * 流式解析器状态管理
 */
interface StreamParserState {
  buffer: string;
  lastProcessedIndex: number;
  isStreamEnded: boolean;
}

// 全局状态，用于存储解析状态
let parserState: StreamParserState = {
  buffer: "",
  lastProcessedIndex: 0,
  isStreamEnded: false,
};

/**
 * JSON解析结果
 */
interface JsonParseResult {
  data: ChunkData | null;
  endIndex: number;
  isComplete: boolean;
  error?: string;
}

/**
 * 优化的流式数据解析器
 * 支持被截断的JSON对象和一次处理多个chunk
 * 每当完成一个完整的JSON解析就清空缓存
 *
 * @param data - 新接收到的数据块
 * @returns 解析后的JSON对象数组
 */
export function parseStreamingData(data: string): ChunkData[] {
  // 将新数据追加到缓存中
  parserState.buffer += data;

  const parsedData: ChunkData[] = [];
  let processedLength = 0;

  // 处理缓冲区开头可能存在的续传JSON数据
  if (
    parserState.buffer.length > 0 &&
    !parserState.buffer.trim().startsWith("data:")
  ) {
    const result = parseJsonAtPosition(parserState.buffer, 0);
    if (result.data) {
      parsedData.push(result.data);
      processedLength = result.endIndex;
      // 完成一个JSON解析，立即清空已处理的部分
      parserState.buffer = parserState.buffer.substring(processedLength);
      processedLength = 0;
    } else if (!result.isComplete) {
      // 数据不完整，保留缓存等待更多数据
      console.log("续传数据不完整，等待更多数据");
      return parsedData;
    }
  }

  // 处理所有的 "data:" 块
  while (processedLength < parserState.buffer.length) {
    const dataIndex = parserState.buffer.indexOf("data:", processedLength);
    if (dataIndex === -1) {
      break;
    }

    // 跳过 "data:" 前缀和空白字符
    const jsonStart = findJsonStart(parserState.buffer, dataIndex + 5);
    if (jsonStart === -1) {
      processedLength = dataIndex + 5;
      continue;
    }

    // 解析JSON对象
    const result = parseJsonAtPosition(parserState.buffer, jsonStart);
    if (result.data) {
      parsedData.push(result.data);
      const endIndex = result.endIndex;

      // 完成一个JSON解析，立即清空已处理的部分
      parserState.buffer = parserState.buffer.substring(endIndex);
      processedLength = 0; // 重置处理位置，因为缓冲区已更新

      // 继续处理剩余的数据，可能还有其他完整的data块
      continue;
    } else if (!result.isComplete) {
      // 数据不完整，但保留从当前dataIndex开始的所有未处理数据
      // 这样可以保证不完整的data块能在下次接收数据时继续处理
      const remainingData = parserState.buffer.substring(dataIndex);
      parserState.buffer = remainingData;
      console.log("JSON数据不完整，保留未处理数据，等待更多数据");
      break;
    } else {
      // 跳过无效的数据块
      processedLength = dataIndex + 5;
    }
  }

  console.log("解析到的数据条数:", parsedData);
  console.log("缓存的数据长度:", parserState.buffer.length);

  return parsedData;
}

/**
 * 查找JSON开始位置
 */
function findJsonStart(buffer: string, startIndex: number): number {
  let index = startIndex;

  // 跳过空白字符
  while (index < buffer.length && /\s/.test(buffer[index])) {
    index++;
  }

  // 检查是否找到JSON开始标记
  if (index < buffer.length && buffer[index] === "{") {
    return index;
  }

  return -1;
}

/**
 * 在指定位置解析JSON对象
 */
function parseJsonAtPosition(
  buffer: string,
  startIndex: number,
): JsonParseResult {
  if (startIndex >= buffer.length || buffer[startIndex] !== "{") {
    return { data: null, endIndex: startIndex, isComplete: false };
  }

  const jsonResult = extractCompleteJsonFromPosition(buffer, startIndex);

  if (!jsonResult.jsonStr) {
    return {
      data: null,
      endIndex: startIndex,
      isComplete: jsonResult.isComplete,
      error: jsonResult.isComplete
        ? "Invalid JSON structure"
        : "Incomplete JSON",
    };
  }

  try {
    const parsed = JSON.parse(jsonResult.jsonStr);
    if (typeof parsed === "object" && parsed !== null) {
      return {
        data: parsed as ChunkData,
        endIndex: startIndex + jsonResult.jsonStr.length,
        isComplete: true,
      };
    }
  } catch (error) {
    console.warn(
      `JSON解析失败: ${jsonResult.jsonStr.substring(0, 100)}...`,
      `错误: ${(error as Error).message}`,
    );
    return {
      data: null,
      endIndex: startIndex + jsonResult.jsonStr.length,
      isComplete: true,
      error: (error as Error).message,
    };
  }

  return { data: null, endIndex: startIndex, isComplete: false };
}

/**
 * 从指定位置开始提取完整的JSON对象字符串
 * 返回解析结果，包含JSON字符串和是否完整的标识
 */
function extractCompleteJsonFromPosition(
  data: string,
  startIndex: number,
): {
  jsonStr: string | null;
  isComplete: boolean;
} {
  const len = data.length;
  if (startIndex >= len || data[startIndex] !== "{") {
    return { jsonStr: null, isComplete: false };
  }

  // 动态规划状态定义
  const dp: Array<{
    inString: boolean;
    escapeNext: boolean;
    braceCount: number;
    isValid: boolean;
  }> = Array.from({ length: len - startIndex });

  // 初始状态
  dp[0] = {
    inString: false,
    escapeNext: false,
    braceCount: 1, // 第一个字符是 '{'
    isValid: true,
  };

  // 动态规划转移
  for (let i = 1; i < len - startIndex; i++) {
    const char = data[startIndex + i];
    const prev = dp[i - 1];

    // 如果前一个状态无效，当前状态也无效
    if (!prev.isValid) {
      dp[i] = { ...prev, isValid: false };
      continue;
    }

    const currentState = {
      inString: prev.inString,
      escapeNext: false,
      braceCount: prev.braceCount,
      isValid: true,
    };

    // 处理转义字符
    if (prev.escapeNext) {
      // 前一个字符是转义字符，当前字符被转义
      dp[i] = currentState;
      continue;
    }

    if (char === "\\") {
      currentState.escapeNext = true;
    } else if (char === '"') {
      // 引号切换字符串状态
      currentState.inString = !prev.inString;
    } else if (!prev.inString) {
      // 不在字符串内部，处理括号
      if (char === "{") {
        currentState.braceCount++;
      } else if (char === "}") {
        currentState.braceCount--;

        // 找到完整的JSON对象
        if (currentState.braceCount === 0) {
          return {
            jsonStr: data.substring(startIndex, startIndex + i + 1),
            isComplete: true,
          };
        }

        // 括号不匹配，状态无效
        if (currentState.braceCount < 0) {
          currentState.isValid = false;
        }
      }
    }

    dp[i] = currentState;
  }

  return { jsonStr: null, isComplete: false };
}

/**
 * 批量处理多个数据块
 * 适用于一次性接收到多个chunk的情况
 *
 * @param chunks - 数据块数组
 * @returns 解析后的JSON对象数组
 */
export function parseStreamingDataBatch(chunks: string[]): ChunkData[] {
  const allParsedData: ChunkData[] = [];

  for (let i = 0; i < chunks.length; i++) {
    const parsedData = parseStreamingData(chunks[i]);
    allParsedData.push(...parsedData);
  }

  return allParsedData;
}

/**
 * 获取解析器当前状态信息
 * 用于调试和监控
 */
export function getParserState(): Readonly<StreamParserState> {
  return { ...parserState };
}

/**
 * 检查缓冲区是否有未处理的数据
 */
export function hasUnprocessedData(): boolean {
  return parserState.buffer.length > 0;
}

/**
 * 强制处理缓冲区中的剩余数据
 * 在流意外中断时使用
 */
export function flushRemainingData(): ChunkData[] {
  if (parserState.buffer.length === 0) {
    return [];
  }

  // 尝试解析缓冲区中的剩余数据
  const remainingData = parserState.buffer;
  resetStreamingParser(); // 清空缓冲区

  // 如果剩余数据看起来像是不完整的JSON，尝试解析
  const results: ChunkData[] = [];
  if (remainingData.trim()) {
    console.warn("强制处理剩余数据:", `${remainingData.substring(0, 100)}...`);
    // 这里可以尝试一些恢复策略，但通常不完整的数据无法解析
  }

  return results;
}

/**
 * 重置解析器的内部状态
 */
export function resetStreamingParser(): void {
  parserState = {
    buffer: "",
    lastProcessedIndex: 0,
    isStreamEnded: false,
  };
}
