/**
 * 测试数据拼接功能
 */

import { parseStreamingData, resetStreamingParser, hasUnprocessedData, getParserState } from "./useParseStreaming";

console.log("=== 测试数据拼接功能 ===");

/**
 * 测试1: 基本的JSON截断拼接
 */
function testBasicConcatenation() {
  console.log("\n1. 测试基本JSON截断拼接:");
  resetStreamingParser();

  const completeJson = 'data: {"message": {"content": "Hello World", "message_id": "msg1"}, "event_type": "message"}';
  
  // 分割成两部分
  const part1 = completeJson.substring(0, 40); // 'data: {"message": {"content": "Hello Wo'
  const part2 = completeJson.substring(40);    // 'rld", "message_id": "msg1"}, "event_type": "message"}'
  
  console.log("Part1:", part1);
  console.log("Part2:", part2);
  
  // 第一部分
  const result1 = parseStreamingData(part1);
  console.log("处理part1后:");
  console.log("  解析结果数量:", result1.length);
  console.log("  有未处理数据:", hasUnprocessedData());
  console.log("  缓冲区内容:", getParserState().buffer);
  
  // 第二部分
  const result2 = parseStreamingData(part2);
  console.log("处理part2后:");
  console.log("  解析结果数量:", result2.length);
  console.log("  有未处理数据:", hasUnprocessedData());
  console.log("  缓冲区内容:", getParserState().buffer);
  
  const totalResults = result1.length + result2.length;
  console.log("总共解析到:", totalResults, "条消息");
  
  if (totalResults === 1) {
    console.log("✅ 基本拼接测试通过");
  } else {
    console.log("❌ 基本拼接测试失败");
  }
}

/**
 * 测试2: data:前缀被截断
 */
function testDataPrefixConcatenation() {
  console.log("\n2. 测试data:前缀截断拼接:");
  resetStreamingParser();

  const chunk1 = "da";
  const chunk2 = "ta: ";
  const chunk3 = '{"message": {"content": "Test"}, "event_type": "message"}';
  
  console.log("Chunk1:", chunk1);
  console.log("Chunk2:", chunk2);
  console.log("Chunk3:", chunk3);
  
  const result1 = parseStreamingData(chunk1);
  console.log("处理chunk1后: 解析数量=", result1.length, ", 缓冲区=", getParserState().buffer);
  
  const result2 = parseStreamingData(chunk2);
  console.log("处理chunk2后: 解析数量=", result2.length, ", 缓冲区=", getParserState().buffer);
  
  const result3 = parseStreamingData(chunk3);
  console.log("处理chunk3后: 解析数量=", result3.length, ", 缓冲区=", getParserState().buffer);
  
  const totalResults = result1.length + result2.length + result3.length;
  console.log("总共解析到:", totalResults, "条消息");
  
  if (totalResults === 1) {
    console.log("✅ data:前缀拼接测试通过");
  } else {
    console.log("❌ data:前缀拼接测试失败");
  }
}

/**
 * 测试3: 多次截断拼接
 */
function testMultipleConcatenation() {
  console.log("\n3. 测试多次截断拼接:");
  resetStreamingParser();

  const completeData = 'data: {"message": {"content": "这是一个需要多次拼接的长消息", "message_id": "msg1"}, "event_type": "message"}';
  
  // 分割成5个小块
  const chunkSize = Math.ceil(completeData.length / 5);
  const chunks = [];
  for (let i = 0; i < completeData.length; i += chunkSize) {
    chunks.push(completeData.substring(i, i + chunkSize));
  }
  
  console.log("分割成", chunks.length, "个chunk:");
  chunks.forEach((chunk, index) => {
    console.log(`  Chunk${index + 1}:`, chunk);
  });
  
  let totalResults = 0;
  chunks.forEach((chunk, index) => {
    const results = parseStreamingData(chunk);
    totalResults += results.length;
    console.log(`处理chunk${index + 1}后: 解析数量=${results.length}, 缓冲区长度=${getParserState().buffer.length}`);
  });
  
  console.log("总共解析到:", totalResults, "条消息");
  
  if (totalResults === 1) {
    console.log("✅ 多次拼接测试通过");
  } else {
    console.log("❌ 多次拼接测试失败");
  }
}

/**
 * 测试4: 混合完整和截断数据
 */
function testMixedConcatenation() {
  console.log("\n4. 测试混合完整和截断数据:");
  resetStreamingParser();

  // 第一个chunk: 一个完整的data块 + 一个不完整的开始
  const chunk1 = `data: {"message": {"content": "完整消息1"}, "event_type": "message"}
data: {"message": {"content": "不完整`;

  // 第二个chunk: 不完整的结束 + 一个新的完整data块
  const chunk2 = `消息2"}, "event_type": "message"}
data: {"message": {"content": "完整消息3"}, "event_type": "message"}`;

  console.log("Chunk1包含: 1个完整 + 1个不完整开始");
  console.log("Chunk2包含: 1个不完整结束 + 1个完整");

  const result1 = parseStreamingData(chunk1);
  console.log("处理chunk1后:");
  console.log("  解析数量:", result1.length);
  console.log("  缓冲区:", getParserState().buffer);

  const result2 = parseStreamingData(chunk2);
  console.log("处理chunk2后:");
  console.log("  解析数量:", result2.length);
  console.log("  缓冲区:", getParserState().buffer);

  const totalResults = result1.length + result2.length;
  console.log("总共解析到:", totalResults, "条消息");

  if (totalResults === 3) {
    console.log("✅ 混合拼接测试通过");
  } else {
    console.log("❌ 混合拼接测试失败");
  }
}

/**
 * 运行所有拼接测试
 */
export function runConcatenationTests() {
  console.log("开始数据拼接测试...");
  
  testBasicConcatenation();
  testDataPrefixConcatenation();
  testMultipleConcatenation();
  testMixedConcatenation();
  
  console.log("\n=== 拼接测试完成 ===");
}

// 如果直接运行此文件，执行测试
if (typeof window === "undefined") {
  runConcatenationTests();
}
