/**
 * 调试测试文件
 * 用于诊断解析器问题
 */

import { parseStreamingData, resetStreamingParser } from "./useParseStreaming";

/**
 * 测试基本解析功能
 */
function testBasicParsing() {
  console.log("=== 测试基本解析功能 ===");
  resetStreamingParser();

  // 测试最简单的情况
  const simpleData = 'data: {"message": {"content": "Hello World", "message_id": "msg1"}, "event_type": "message"}';
  
  console.log("测试数据:", simpleData);
  console.log("开始解析...");
  
  const results = parseStreamingData(simpleData);
  
  console.log("解析结果:", results);
  console.log("结果数量:", results.length);
  
  if (results.length > 0) {
    console.log("第一条消息:", results[0]);
  } else {
    console.log("❌ 没有解析到任何数据");
  }
}

/**
 * 测试JSON结构
 */
function testJsonStructure() {
  console.log("\n=== 测试JSON结构 ===");
  resetStreamingParser();

  // 测试符合ChunkData接口的数据
  const validChunkData = {
    message: {
      session_id: "test-session",
      message_id: "msg1", 
      intention_id: 1,
      event_time: Date.now(),
      content: "Test message",
      think: "",
      description: "Test description",
      id: "msg1",
      duration: 0,
      is_card: false,
      event_type: "message"
    },
    event_type: "message"
  };

  const dataString = `data: ${JSON.stringify(validChunkData)}`;
  
  console.log("有效的ChunkData JSON:");
  console.log(dataString);
  console.log("开始解析...");
  
  const results = parseStreamingData(dataString);
  
  console.log("解析结果:", results);
  console.log("结果数量:", results.length);
}

/**
 * 测试data前缀处理
 */
function testDataPrefix() {
  console.log("\n=== 测试data前缀处理 ===");
  resetStreamingParser();

  const testCases = [
    'data: {"test": "value"}',
    'data:{"test": "value"}',
    'data:   {"test": "value"}',
    '  data: {"test": "value"}  ',
  ];

  testCases.forEach((testCase, index) => {
    console.log(`\n测试用例 ${index + 1}: "${testCase}"`);
    resetStreamingParser();
    
    const results = parseStreamingData(testCase);
    console.log(`结果数量: ${results.length}`);
  });
}

/**
 * 测试JSON边界检测
 */
function testJsonBoundaryDetection() {
  console.log("\n=== 测试JSON边界检测 ===");
  resetStreamingParser();

  // 测试嵌套JSON
  const nestedJson = {
    message: {
      content: "Test with nested object",
      metadata: {
        timestamp: Date.now(),
        source: "test"
      }
    }
  };

  const dataString = `data: ${JSON.stringify(nestedJson)}`;
  
  console.log("嵌套JSON测试:");
  console.log(dataString);
  
  const results = parseStreamingData(dataString);
  console.log("解析结果数量:", results.length);
}

/**
 * 逐步调试解析过程
 */
function stepByStepDebug() {
  console.log("\n=== 逐步调试解析过程 ===");
  resetStreamingParser();

  const testData = 'data: {"message": {"content": "Debug test"}, "event_type": "message"}';
  
  console.log("1. 输入数据:", testData);
  console.log("2. 查找data:位置:", testData.indexOf("data:"));
  console.log("3. JSON开始位置:", testData.indexOf("{"));
  
  // 手动测试JSON提取
  const jsonStart = testData.indexOf("{");
  if (jsonStart !== -1) {
    const jsonPart = testData.substring(jsonStart);
    console.log("4. 提取的JSON部分:", jsonPart);
    
    try {
      const parsed = JSON.parse(jsonPart);
      console.log("5. JSON解析成功:", parsed);
    } catch (error) {
      console.log("5. JSON解析失败:", error);
    }
  }
  
  console.log("6. 使用解析器:");
  const results = parseStreamingData(testData);
  console.log("7. 最终结果:", results);
}

/**
 * 运行所有调试测试
 */
export function runDebugTests() {
  console.log("开始调试测试...\n");
  
  testBasicParsing();
  testJsonStructure();
  testDataPrefix();
  testJsonBoundaryDetection();
  stepByStepDebug();
  
  console.log("\n调试测试完成！");
}

// 如果直接运行此文件，执行调试测试
if (typeof window === "undefined") {
  runDebugTests();
}
