/**
 * 性能对比测试
 * 比较优化前后的解析器性能
 */

import { 
  parseStreamingData, 
  parseStreamingDataBatch, 
  resetStreamingParser 
} from "./useParseStreaming";

/**
 * 生成测试数据
 */
function generateTestData(count: number): string[] {
  const chunks: string[] = [];
  for (let i = 0; i < count; i++) {
    const data = {
      message: {
        session_id: `session-${i}`,
        message_id: `msg-${i}`,
        intention_id: i,
        event_time: Date.now(),
        content: `Performance test message ${i} with some longer content to simulate real-world usage. This message contains multiple sentences and various characters to test parsing performance under realistic conditions.`,
        think: "",
        description: `Description for message ${i}`,
        id: `id-${i}`,
        duration: Math.random() * 1000,
        is_card: i % 10 === 0,
        event_type: i === count - 1 ? "message_end" : "message"
      },
      event_type: i === count - 1 ? "message_end" : "message"
    };
    chunks.push(`data: ${JSON.stringify(data)}`);
  }
  return chunks;
}

/**
 * 生成被截断的测试数据
 */
function generateTruncatedTestData(count: number): string[] {
  const chunks: string[] = [];
  const fullData = generateTestData(count);
  
  // 将一些完整的数据块截断
  for (let i = 0; i < fullData.length; i++) {
    const data = fullData[i];
    if (i % 3 === 0 && i < fullData.length - 1) {
      // 截断这个数据块
      const midPoint = Math.floor(data.length * 0.6);
      chunks.push(data.substring(0, midPoint));
      chunks.push(data.substring(midPoint));
    } else {
      chunks.push(data);
    }
  }
  
  return chunks;
}

/**
 * 测试单个数据块处理性能
 */
function testSingleChunkPerformance() {
  console.log("=== 单个数据块处理性能测试 ===");
  
  const testSizes = [100, 500, 1000, 2000];
  
  testSizes.forEach(size => {
    resetStreamingParser();
    const testData = generateTestData(size).join("\n");
    
    const startTime = performance.now();
    const results = parseStreamingData(testData, true);
    const endTime = performance.now();
    
    const duration = endTime - startTime;
    const throughput = size / duration * 1000; // 消息/秒
    
    console.log(`${size} 条消息:`);
    console.log(`  - 处理时间: ${duration.toFixed(2)}ms`);
    console.log(`  - 吞吐量: ${throughput.toFixed(0)} 消息/秒`);
    console.log(`  - 解析结果: ${results.length} 条`);
    console.log(`  - 平均每条: ${(duration / size).toFixed(3)}ms`);
    console.log("");
  });
}

/**
 * 测试批量处理性能
 */
function testBatchProcessingPerformance() {
  console.log("=== 批量处理性能测试 ===");
  
  const batchSizes = [50, 100, 200, 500];
  
  batchSizes.forEach(size => {
    const chunks = generateTestData(size);
    
    // 测试逐个处理
    resetStreamingParser();
    const startTime1 = performance.now();
    let totalResults1 = 0;
    chunks.forEach((chunk, index) => {
      const isLast = index === chunks.length - 1;
      const results = parseStreamingData(chunk, isLast);
      totalResults1 += results.length;
    });
    const endTime1 = performance.now();
    const duration1 = endTime1 - startTime1;
    
    // 测试批量处理
    resetStreamingParser();
    const startTime2 = performance.now();
    const results2 = parseStreamingDataBatch(chunks, true);
    const endTime2 = performance.now();
    const duration2 = endTime2 - startTime2;
    
    const improvement = ((duration1 - duration2) / duration1 * 100);
    
    console.log(`${size} 个数据块:`);
    console.log(`  - 逐个处理: ${duration1.toFixed(2)}ms (${totalResults1} 条)`);
    console.log(`  - 批量处理: ${duration2.toFixed(2)}ms (${results2.length} 条)`);
    console.log(`  - 性能提升: ${improvement.toFixed(1)}%`);
    console.log("");
  });
}

/**
 * 测试截断数据处理性能
 */
function testTruncatedDataPerformance() {
  console.log("=== 截断数据处理性能测试 ===");
  
  const testSizes = [100, 300, 500];
  
  testSizes.forEach(size => {
    const truncatedChunks = generateTruncatedTestData(size);
    
    resetStreamingParser();
    const startTime = performance.now();
    let totalResults = 0;
    
    truncatedChunks.forEach((chunk, index) => {
      const isLast = index === truncatedChunks.length - 1;
      const results = parseStreamingData(chunk, isLast);
      totalResults += results.length;
    });
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    console.log(`${size} 条原始消息 (${truncatedChunks.length} 个截断块):`);
    console.log(`  - 处理时间: ${duration.toFixed(2)}ms`);
    console.log(`  - 解析结果: ${totalResults} 条`);
    console.log(`  - 平均每块: ${(duration / truncatedChunks.length).toFixed(3)}ms`);
    console.log("");
  });
}

/**
 * 测试内存使用情况
 */
function testMemoryUsage() {
  console.log("=== 内存使用测试 ===");
  
  // 模拟大量小数据块
  const smallChunks = Array.from({ length: 1000 }, (_, i) => 
    `data: {"message": {"content": "Small message ${i}", "message_id": "msg${i}"}}`
  );
  
  resetStreamingParser();
  const startTime = performance.now();
  
  let totalResults = 0;
  smallChunks.forEach((chunk, index) => {
    const isLast = index === smallChunks.length - 1;
    const results = parseStreamingData(chunk, isLast);
    totalResults += results.length;
  });
  
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  console.log("1000 个小数据块:");
  console.log(`  - 处理时间: ${duration.toFixed(2)}ms`);
  console.log(`  - 解析结果: ${totalResults} 条`);
  console.log(`  - 内存效率: ${(totalResults / duration * 1000).toFixed(0)} 消息/秒`);
  console.log("");
}

/**
 * 测试错误恢复性能
 */
function testErrorRecoveryPerformance() {
  console.log("=== 错误恢复性能测试 ===");
  
  const mixedData = [
    'data: {"message": {"content": "Valid message 1", "message_id": "msg1"}}',
    'data: {"invalid": json}', // 无效JSON
    'data: {"message": {"content": "Valid message 2", "message_id": "msg2"}}',
    'data: {"message": {"content": "Incomplete', // 截断
    ' message", "message_id": "msg3"}}',
    'data: {"message": {"content": "Valid message 4", "message_id": "msg4"}}'
  ];
  
  resetStreamingParser();
  const startTime = performance.now();
  
  let totalResults = 0;
  mixedData.forEach((chunk, index) => {
    const isLast = index === mixedData.length - 1;
    const results = parseStreamingData(chunk, isLast);
    totalResults += results.length;
  });
  
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  console.log("混合数据（包含错误和截断）:");
  console.log(`  - 处理时间: ${duration.toFixed(2)}ms`);
  console.log(`  - 解析结果: ${totalResults} 条`);
  console.log(`  - 错误恢复: 成功处理有效数据`);
  console.log("");
}

/**
 * 运行所有性能测试
 */
export function runPerformanceTests() {
  console.log("开始性能测试...\n");
  
  const overallStartTime = performance.now();
  
  testSingleChunkPerformance();
  testBatchProcessingPerformance();
  testTruncatedDataPerformance();
  testMemoryUsage();
  testErrorRecoveryPerformance();
  
  const overallEndTime = performance.now();
  const totalDuration = overallEndTime - overallStartTime;
  
  console.log("=== 性能测试总结 ===");
  console.log(`总测试时间: ${totalDuration.toFixed(2)}ms`);
  console.log("✅ 所有性能测试完成");
  
  // 性能建议
  console.log("\n=== 性能优化建议 ===");
  console.log("1. 对于大量数据，使用 parseStreamingDataBatch() 获得更好性能");
  console.log("2. 明确标记流结束 (isStreamEnded=true) 以启用优化");
  console.log("3. 定期检查 hasUnprocessedData() 以监控解析状态");
  console.log("4. 在流中断时使用 flushRemainingData() 处理剩余数据");
}

// 如果直接运行此文件，执行性能测试
if (typeof window === "undefined") {
  runPerformanceTests();
}
