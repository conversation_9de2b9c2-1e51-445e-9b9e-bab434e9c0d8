/**
 * 测试修复后的解析器
 */

import { parseStreamingData, resetStreamingParser } from "./useParseStreaming";

console.log("=== 测试修复后的解析器 ===");

// 重置解析器
resetStreamingParser();

// 测试1: 基本解析
console.log("\n1. 测试基本解析:");
const basicData = 'data: {"message": {"content": "Hello World", "message_id": "msg1"}, "event_type": "message"}';
console.log("输入:", basicData);

const result1 = parseStreamingData(basicData);
console.log("结果数量:", result1.length);
console.log("结果:", result1);

// 测试2: 混合完整和不完整数据
console.log("\n2. 测试混合数据:");
resetStreamingParser();

const mixedData = `data: {"message": {"content": "Complete message", "message_id": "msg1"}, "event_type": "message"}
data: {"message": {"content": "Incomplete`;

console.log("输入混合数据...");
const result2 = parseStreamingData(mixedData);
console.log("第一次解析结果数量:", result2.length);

const completion = ' message", "message_id": "msg2"}, "event_type": "message"}';
console.log("补充数据...");
const result3 = parseStreamingData(completion);
console.log("第二次解析结果数量:", result3.length);

// 测试3: 简单JSON
console.log("\n3. 测试简单JSON:");
resetStreamingParser();

const simpleJson = 'data: {"test": "value"}';
console.log("输入:", simpleJson);
const result4 = parseStreamingData(simpleJson);
console.log("结果数量:", result4.length);
console.log("结果:", result4);

console.log("\n=== 测试完成 ===");
