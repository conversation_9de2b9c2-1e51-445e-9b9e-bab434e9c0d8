/**
 * 优化后的流式解析器使用示例
 */

import { 
  parseStreamingData, 
  parseStreamingDataBatch, 
  resetStreamingParser,
  hasUnprocessedData,
  flushRemainingData,
  getParserState
} from "./useParseStreaming";

/**
 * 示例1: 处理被截断的JSON数据
 */
export function exampleTruncatedData() {
  console.log("=== 示例1: 处理被截断的JSON数据 ===");
  
  // 重置解析器状态
  resetStreamingParser();
  
  // 模拟被截断的数据
  const chunk1 = 'data: {"message": {"session_id": "123", "content": "Hello';
  const chunk2 = ' World", "message_id": "msg1"}, "event_type": "message"}';
  
  console.log("处理第一个chunk（被截断）:");
  const result1 = parseStreamingData(chunk1);
  console.log("解析结果:", result1);
  console.log("是否有未处理数据:", hasUnprocessedData());
  
  console.log("\n处理第二个chunk（完成）:");
  const result2 = parseStreamingData(chunk2, true);
  console.log("解析结果:", result2);
  console.log("是否有未处理数据:", hasUnprocessedData());
}

/**
 * 示例2: 处理多个完整的JSON对象
 */
export function exampleMultipleChunks() {
  console.log("\n=== 示例2: 处理多个完整的JSON对象 ===");
  
  resetStreamingParser();
  
  const multipleData = `
data: {"message": {"session_id": "123", "content": "First message", "message_id": "msg1"}, "event_type": "message"}
data: {"message": {"session_id": "123", "content": "Second message", "message_id": "msg2"}, "event_type": "message"}
data: {"message": {"session_id": "123", "content": "Third message", "message_id": "msg3"}, "event_type": "message_end"}
  `;
  
  const results = parseStreamingData(multipleData, true);
  console.log("解析到的消息数量:", results.length);
  results.forEach((result, index) => {
    console.log(`消息 ${index + 1}:`, result.message.content);
  });
}

/**
 * 示例3: 批量处理多个数据块
 */
export function exampleBatchProcessing() {
  console.log("\n=== 示例3: 批量处理多个数据块 ===");
  
  resetStreamingParser();
  
  const chunks = [
    'data: {"message": {"session_id": "123", "content": "Batch message 1", "message_id": "msg1"}, "event_type": "message"}',
    'data: {"message": {"session_id": "123", "content": "Batch message 2", "message_id": "msg2"}, "event_type": "message"}',
    'data: {"message": {"session_id": "123", "content": "Batch message 3", "message_id": "msg3"}, "event_type": "message_end"}'
  ];
  
  const results = parseStreamingDataBatch(chunks, true);
  console.log("批量处理结果数量:", results.length);
  results.forEach((result, index) => {
    console.log(`批量消息 ${index + 1}:`, result.message.content);
  });
}

/**
 * 示例4: 处理复杂的截断情况
 */
export function exampleComplexTruncation() {
  console.log("\n=== 示例4: 处理复杂的截断情况 ===");
  
  resetStreamingParser();
  
  // 模拟复杂的截断情况：JSON在字符串内部被截断
  const chunks = [
    'data: {"message": {"session_id": "123", "content": "This is a very long message that contains \\"quoted text\\" and',
    ' might be truncated in the middle of a string", "message_id": "msg1"}, "event_type": "message"}',
    '\ndata: {"message": {"session_id": "123", "content": "Another message", "message_id": "msg2"}, "event_type": "message_end"}'
  ];
  
  let allResults: any[] = [];
  chunks.forEach((chunk, index) => {
    const isLast = index === chunks.length - 1;
    const results = parseStreamingData(chunk, isLast);
    allResults.push(...results);
    console.log(`处理chunk ${index + 1}, 解析到 ${results.length} 条消息`);
  });
  
  console.log("总共解析到的消息:", allResults.length);
  allResults.forEach((result, index) => {
    console.log(`消息 ${index + 1}:`, result.message.content);
  });
}

/**
 * 示例5: 错误处理和状态监控
 */
export function exampleErrorHandling() {
  console.log("\n=== 示例5: 错误处理和状态监控 ===");
  
  resetStreamingParser();
  
  // 模拟无效的JSON数据
  const invalidData = 'data: {"message": {"session_id": "123", "content": "Invalid JSON"'; // 缺少闭合括号
  
  console.log("处理无效数据:");
  const results = parseStreamingData(invalidData);
  console.log("解析结果:", results);
  
  console.log("解析器状态:", getParserState());
  console.log("是否有未处理数据:", hasUnprocessedData());
  
  // 强制刷新剩余数据
  console.log("强制刷新剩余数据:");
  const flushedResults = flushRemainingData();
  console.log("刷新结果:", flushedResults);
  console.log("刷新后是否还有未处理数据:", hasUnprocessedData());
}

/**
 * 运行所有示例
 */
export function runAllExamples() {
  console.log("开始运行流式解析器优化示例...\n");
  
  exampleTruncatedData();
  exampleMultipleChunks();
  exampleBatchProcessing();
  exampleComplexTruncation();
  exampleErrorHandling();
  
  console.log("\n所有示例运行完成！");
}

// 如果直接运行此文件，执行所有示例
if (typeof window === "undefined") {
  // Node.js 环境
  runAllExamples();
}
