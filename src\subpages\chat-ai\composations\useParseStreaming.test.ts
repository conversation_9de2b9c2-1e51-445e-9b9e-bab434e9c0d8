/**
 * 流式解析器测试用例
 */

import {
  parseStreamingData,
  parseStreamingDataBatch,
  resetStreamingParser,
  hasUnprocessedData,
  flushRemainingData,
} from "./useParseStreaming";
import type { ChunkData } from "@/api/servers/chat/type";

/**
 * 创建测试用的ChunkData
 */
function createTestChunkData(
  content: string,
  messageId: string,
  eventType = "message",
): string {
  return `data: {"message": {"session_id": "test-session", "content": "${content}", "message_id": "${messageId}", "intention_id": 1, "event_time": ${Date.now()}, "think": "", "description": "", "id": "${messageId}", "duration": 0, "is_card": false, "event_type": "${eventType}"}, "event_type": "${eventType}"}`;
}

/**
 * 测试基本功能
 */
function testBasicParsing() {
  console.log("测试基本解析功能...");
  resetStreamingParser();

  const testData = createTestChunkData("Hello World", "msg1");
  const results = parseStreamingData(testData);

  console.assert(results.length === 1, "应该解析出1条消息");
  console.assert(
    results[0].message.content === "Hello World",
    "消息内容应该正确",
  );
  console.assert(results[0].message.message_id === "msg1", "消息ID应该正确");
  console.log("✓ 基本解析功能测试通过");
}

/**
 * 测试截断处理
 */
function testTruncatedData() {
  console.log("测试截断数据处理...");
  resetStreamingParser();

  const chunk1 = 'data: {"message": {"session_id": "test", "content": "Hello';
  const chunk2 =
    ' World", "message_id": "msg1", "intention_id": 1, "event_time": 123456, "think": "", "description": "", "id": "msg1", "duration": 0, "is_card": false, "event_type": "message"}, "event_type": "message"}';

  // 第一个chunk应该没有解析出完整数据
  const result1 = parseStreamingData(chunk1);
  console.assert(result1.length === 0, "第一个chunk不应该解析出数据");
  console.assert(hasUnprocessedData(), "应该有未处理的数据");

  // 第二个chunk应该解析出完整数据
  const result2 = parseStreamingData(chunk2);
  console.assert(result2.length === 1, "第二个chunk应该解析出1条消息");
  console.assert(
    result2[0].message.content === "Hello World",
    "拼接后的内容应该正确",
  );
  console.log("✓ 截断数据处理测试通过");
}

/**
 * 测试多个JSON对象
 */
function testMultipleObjects() {
  console.log("测试多个JSON对象解析...");
  resetStreamingParser();

  const multipleData = [
    createTestChunkData("Message 1", "msg1"),
    createTestChunkData("Message 2", "msg2"),
    createTestChunkData("Message 3", "msg3", "message_end"),
  ].join("\n");

  const results = parseStreamingData(multipleData);
  console.assert(results.length === 3, "应该解析出3条消息");
  console.assert(
    results[0].message.content === "Message 1",
    "第一条消息内容正确",
  );
  console.assert(
    results[1].message.content === "Message 2",
    "第二条消息内容正确",
  );
  console.assert(
    results[2].message.content === "Message 3",
    "第三条消息内容正确",
  );
  console.assert(
    results[2].event_type === "message_end",
    "最后一条消息类型正确",
  );
  console.log("✓ 多个JSON对象解析测试通过");
}

/**
 * 测试批量处理
 */
function testBatchProcessing() {
  console.log("测试批量处理...");
  resetStreamingParser();

  const chunks = [
    createTestChunkData("Batch 1", "batch1"),
    createTestChunkData("Batch 2", "batch2"),
    createTestChunkData("Batch 3", "batch3"),
  ];

  const results = parseStreamingDataBatch(chunks);
  console.assert(results.length === 3, "批量处理应该解析出3条消息");
  console.assert(results[0].message.content === "Batch 1", "批量消息1内容正确");
  console.assert(results[1].message.content === "Batch 2", "批量消息2内容正确");
  console.assert(results[2].message.content === "Batch 3", "批量消息3内容正确");
  console.log("✓ 批量处理测试通过");
}

/**
 * 测试复杂截断情况
 */
function testComplexTruncation() {
  console.log("测试复杂截断情况...");
  resetStreamingParser();

  // 在字符串内部截断，包含转义字符
  const chunk1 =
    'data: {"message": {"session_id": "test", "content": "This message contains \\"quotes\\" and';
  const chunk2 =
    ' continues here", "message_id": "msg1", "intention_id": 1, "event_time": 123456, "think": "", "description": "", "id": "msg1", "duration": 0, "is_card": false, "event_type": "message"}, "event_type": "message"}';

  const result1 = parseStreamingData(chunk1);
  console.assert(result1.length === 0, "复杂截断第一部分不应该解析出数据");

  const result2 = parseStreamingData(chunk2);
  console.assert(result2.length === 1, "复杂截断第二部分应该解析出1条消息");
  console.assert(
    result2[0].message.content.includes("quotes"),
    "应该正确处理转义字符",
  );
  console.log("✓ 复杂截断情况测试通过");
}

/**
 * 测试错误处理
 */
function testErrorHandling() {
  console.log("测试错误处理...");
  resetStreamingParser();

  // 无效的JSON
  const invalidJson = 'data: {"message": {"invalid": json}';
  const results = parseStreamingData(invalidJson);
  console.assert(results.length === 0, "无效JSON不应该解析出数据");

  // 强制刷新应该清空缓冲区
  flushRemainingData();
  console.assert(!hasUnprocessedData(), "强制刷新后不应该有未处理数据");
  console.log("✓ 错误处理测试通过");
}

/**
 * 测试状态管理
 */
function testStateManagement() {
  console.log("测试状态管理...");
  resetStreamingParser();

  console.assert(!hasUnprocessedData(), "重置后不应该有未处理数据");

  // 添加一些不完整的数据
  parseStreamingData('data: {"incomplete": ');
  console.assert(hasUnprocessedData(), "不完整数据后应该有未处理数据");

  // 重置状态
  resetStreamingParser();
  console.assert(!hasUnprocessedData(), "重置后不应该有未处理数据");
  console.log("✓ 状态管理测试通过");
}

/**
 * 性能测试
 */
function testPerformance() {
  console.log("测试性能...");
  resetStreamingParser();

  const startTime = Date.now();
  const largeData = Array.from({ length: 1000 }, (_, i) =>
    createTestChunkData(`Performance test message ${i}`, `perf${i}`),
  ).join("\n");

  const results = parseStreamingData(largeData);
  const endTime = Date.now();

  console.assert(results.length === 1000, "应该解析出1000条消息");
  console.log(`✓ 性能测试通过 - 处理1000条消息耗时: ${endTime - startTime}ms`);
}

/**
 * 运行所有测试
 */
export function runAllTests() {
  console.log("开始运行流式解析器测试...\n");

  try {
    testBasicParsing();
    testTruncatedData();
    testMultipleObjects();
    testBatchProcessing();
    testComplexTruncation();
    testErrorHandling();
    testStateManagement();
    testPerformance();

    console.log("\n🎉 所有测试通过！");
  } catch (error) {
    console.error("❌ 测试失败:", error);
  }
}

// 如果直接运行此文件，执行所有测试
if (typeof window === "undefined") {
  runAllTests();
}
