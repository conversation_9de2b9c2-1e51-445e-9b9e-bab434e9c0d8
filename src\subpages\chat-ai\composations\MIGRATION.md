# 流式解析器迁移指南

## 概述

本指南帮助您从旧版本的流式解析器迁移到优化后的新版本。新版本提供了更好的性能、更强的错误处理能力和更多的功能。

## 主要变更

### 1. API 变更

#### 旧版本 API
```typescript
// 旧版本只有一个函数
parseStreamingData(data: string): ChunkData[]
resetStreamingParser(): void
```

#### 新版本 API
```typescript
// 新版本简化了API，每当完成JSON解析就自动清空缓存
parseStreamingData(data: string): ChunkData[]
parseStreamingDataBatch(chunks: string[]): ChunkData[]
resetStreamingParser(): void
hasUnprocessedData(): boolean
flushRemainingData(): ChunkData[]
getParserState(): Readonly<StreamParserState>
```

### 2. 向后兼容性

✅ **完全向后兼容** - 现有代码无需修改即可使用新版本。

```typescript
// 这些代码在新版本中仍然有效
const results = parseStreamingData(chunk);
resetStreamingParser();
```

### 3. 推荐的迁移步骤

#### 步骤 1: 基本迁移（无需修改代码）
现有代码可以直接使用新版本，无需任何修改。

#### 步骤 2: 利用新功能（可选）
根据需要添加新功能：

```typescript
// 旧代码（仍然有效）
const results = parseStreamingData(chunk);

// 新代码 - 批量处理
const results = parseStreamingDataBatch(chunks);

// 新代码 - 状态监控
if (hasUnprocessedData()) {
  const remaining = flushRemainingData();
}
```

## 具体迁移示例

### 示例 1: 基本流式处理

#### 旧版本
```typescript
function handleStreamData(chunk: string) {
  const results = parseStreamingData(chunk);
  results.forEach(processMessage);
}

function endStream() {
  resetStreamingParser();
}
```

#### 新版本（推荐）
```typescript
function handleStreamData(chunk: string, isLast = false) {
  const results = parseStreamingData(chunk, isLast);
  results.forEach(processMessage);

  // 如果是最后一个chunk，解析器会自动重置
  // 无需手动调用 resetStreamingParser()
}

function endStream() {
  // 处理可能剩余的数据
  const remaining = flushRemainingData();
  remaining.forEach(processMessage);
}
```

### 示例 2: 错误处理

#### 旧版本
```typescript
function handleStreamData(chunk: string) {
  try {
    const results = parseStreamingData(chunk);
    results.forEach(processMessage);
  } catch (error) {
    console.error('解析失败:', error);
    resetStreamingParser(); // 手动重置
  }
}
```

#### 新版本（推荐）
```typescript
function handleStreamData(chunk: string, isLast = false) {
  const results = parseStreamingData(chunk, isLast);
  results.forEach(processMessage);

  // 检查是否有未处理的数据（可能表示解析问题）
  if (isLast && hasUnprocessedData()) {
    console.warn('流结束但仍有未处理数据:', getParserState().buffer);
    const remaining = flushRemainingData();
    remaining.forEach(processMessage);
  }
}
```

### 示例 3: 批量处理

#### 旧版本
```typescript
function processBatch(chunks: string[]) {
  chunks.forEach((chunk, index) => {
    const results = parseStreamingData(chunk);
    results.forEach(processMessage);

    if (index === chunks.length - 1) {
      resetStreamingParser();
    }
  });
}
```

#### 新版本（推荐）
```typescript
function processBatch(chunks: string[]) {
  // 方法1: 使用批量处理函数
  const allResults = parseStreamingDataBatch(chunks, true);
  allResults.forEach(processMessage);

  // 方法2: 逐个处理（如果需要中间状态）
  // chunks.forEach((chunk, index) => {
  //   const isLast = index === chunks.length - 1;
  //   const results = parseStreamingData(chunk, isLast);
  //   results.forEach(processMessage);
  // });
}
```

## 性能优化建议

### 1. 使用批量处理
当您一次性接收到多个数据块时，使用 `parseStreamingDataBatch` 可以获得更好的性能：

```typescript
// 低效
chunks.forEach(chunk => {
  const results = parseStreamingData(chunk);
  // 处理结果
});

// 高效
const allResults = parseStreamingDataBatch(chunks, true);
// 处理所有结果
```

### 2. 正确标记流结束
明确标记流结束可以让解析器进行优化：

```typescript
// 基本
const results = parseStreamingData(chunk);

// 优化
const results = parseStreamingData(chunk, isLastChunk);
```

### 3. 监控解析器状态
在开发和调试时，使用状态监控功能：

```typescript
if (process.env.NODE_ENV === 'development') {
  const state = getParserState();
  console.log('解析器状态:', state);

  if (hasUnprocessedData()) {
    console.warn('有未处理的数据');
  }
}
```

## 常见问题

### Q: 新版本会影响现有功能吗？
A: 不会。新版本完全向后兼容，现有代码无需修改。

### Q: 我需要立即迁移吗？
A: 不需要。您可以继续使用现有代码，然后逐步采用新功能。

### Q: 新版本的性能如何？
A: 新版本在处理大量数据和复杂截断情况时性能更好，内存使用也更高效。

### Q: 如何调试解析问题？
A: 使用新的状态监控功能：
```typescript
console.log('解析器状态:', getParserState());
console.log('是否有未处理数据:', hasUnprocessedData());
```

### Q: 什么时候使用 flushRemainingData？
A: 在流意外中断或需要强制处理剩余数据时使用：
```typescript
// 流意外中断时
if (streamInterrupted && hasUnprocessedData()) {
  const remaining = flushRemainingData();
  remaining.forEach(processMessage);
}
```

## 测试建议

迁移后，建议运行以下测试：

1. **基本功能测试**
```typescript
import { runAllTests } from './useParseStreaming.test';
runAllTests();
```

2. **示例测试**
```typescript
import { runAllExamples } from './useParseStreaming.example';
runAllExamples();
```

3. **集成测试**
确保在实际应用中测试各种数据流场景。

## 总结

新版本的流式解析器提供了更好的性能和更多功能，同时保持完全的向后兼容性。您可以：

1. **立即使用** - 无需修改现有代码
2. **逐步迁移** - 根据需要采用新功能
3. **获得收益** - 更好的性能和错误处理

建议在新项目中使用新的 API，在现有项目中逐步迁移。
